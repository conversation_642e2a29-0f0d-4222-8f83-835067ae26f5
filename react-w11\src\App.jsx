import { useState } from "react";
import InputBox from "./InputBox";
import DisplayBox from "./DisplayBox";

import Parent from "./Parent";
import { UserContext } from "./userContext";

function App() {
  const [text, setText] = useState("");
  const [username, setUsername] = useState("Marc");

  return (
    <div style={{padding:"20px", fontFamily:"Arial,sans-serif"}}>
      <h1>React Data Flow</h1>

      <h2>Lifting State</h2>
      <InputBox setText={setText}/>
      <DisplayBox text={text}/>

      <h2>Part 2: Props Drilling</h2>
      <Parent username={username} setUsername={setUsername}/>

      <h2>Part 3: Context API</h2>
      <UserContext.Provider value={{username, setUsername}}>
        <Parent/>
      </UserContext.Provider>
    </div>
  )
}

export default App
