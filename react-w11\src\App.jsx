import { useState } from "react";
import InputBox from "./InputBox";
import DisplayBox from "./DisplayBox";

import Parent from "./Parent";
import { UserContext } from "./userContext";

function App() {
  const [text, setText] = useState("");
  const [propsUsername, setPropsUsername] = useState("Marc");
  const [contextUsername, setContextUsername] = useState("StudentUser");

  return (
    <div style={{padding:"20px", fontFamily:"Arial,sans-serif"}}>
      <h1>React Data Flow</h1>

      <h2>Lifting State</h2>
      <InputBox setText={setText}/>
      <DisplayBox text={text} username={propsUsername}/>

      <h2>Part 2: Props Drilling</h2>
      <Parent username={propsUsername} setUsername={setPropsUsername}/>

      <h2>Part 3: Context API</h2>
      <UserContext.Provider value={{username: contextUsername, setUsername: setContextUsername, setPropsUsername: setPropsUsername}}>
        <Parent username={propsUsername}/>
      </UserContext.Provider>
    </div>
  )
}

export default App
