import { useContext } from "react";
import { UserContext } from "./userContext";

function GrandChild({username}) {

    const userContext = useContext(UserContext);

    return(
        <div style={{border:"1px solid green", padding: "10px", margin:"10px"}}>
            <h5>GrandChild Component</h5>
            <p> Props Drilling Username: {username}</p>

            {userContext ? (
                <>
                    <p>Context API Username: {userContext.username}</p>
                    <button onClick={() => userContext.setUsername("StudentUser")}>
                        Change Username via Context
                    </button>
                </>
            ) : (
                <p> No Context Provided</p>
            )}
        </div>
    );
}

export default GrandChild;