function GrandChild ({username, setUsername}) {
    return(
        <div style={{border: "2px solid #FF9800", padding: "15px", margin: "10px"}}>
            <h3>GrandChild Component</h3>
            <p>Current username: {username}</p>
            <input
                type="text"
                placeholder="Change username..."
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                style={{padding: "5px", margin: "5px"}}
            />
            <p>This demonstrates props drilling - the setUsername function was passed down through Parent → Child → GrandChild</p>
        </div>
    )
}

export default GrandChild;